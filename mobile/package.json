{"name": "mobile", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "android:reactotron": "adb reverse tcp:9090 tcp:9090 && echo \"Please make sure you reload the app and Reactotron App is running\"", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest"}, "dependencies": {"@nozbe/watermelondb": "^0.27.1", "@react-native-async-storage/async-storage": "^1.21.0", "@react-native/gradle-plugin": "^0.79.3", "@react-navigation/native": "^7.1.10", "@react-navigation/stack": "^7.3.3", "@reduxjs/toolkit": "^2.0.1", "axios": "^1.6.7", "react": "19.0.0", "react-native": "0.79.2", "react-native-gesture-handler": "^2.25.0", "react-native-modal": "^14.0.0-rc.1", "react-native-safe-area-context": "^5.4.1", "react-native-screens": "^4.11.1", "react-native-vector-icons": "^10.0.3", "react-redux": "^9.1.0", "redux-saga": "^1.3.0", "rxjs": "^7.8.1", "socket.io-client": "^4.8.1"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "18.0.0", "@react-native-community/cli-platform-android": "18.0.0", "@react-native-community/cli-platform-ios": "18.0.0", "@react-native/babel-preset": "0.79.2", "@react-native/eslint-config": "0.79.2", "@react-native/metro-config": "0.79.2", "@react-native/typescript-config": "0.79.2", "@testing-library/jest-native": "^5.4.3", "@testing-library/react-native": "^13.2.0", "@types/jest": "^29.5.13", "@types/react": "^19.0.0", "@types/react-test-renderer": "^19.0.0", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-test-renderer": "19.0.0", "reactotron-react-native": "^5.1.13", "reactotron-redux": "^3.2.0", "reactotron-redux-saga": "^4.2.3", "typescript": "5.0.4"}, "engines": {"node": ">=18"}}