import { Controller, Get, UseGuards, Request, Param, Inject, forwardRef } from '@nestjs/common';
import { MessageService } from './message.service';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiParam } from '@nestjs/swagger';
import { MessageDto } from './dto/message.dto';
import { ChatGateway } from './chat.gateway';

@ApiTags('messages')
@Controller('api/messages')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
export class MessagesController {
  constructor(
    private readonly messageService: MessageService,
    @Inject(forwardRef(() => ChatGateway))
    private readonly chatGateway: ChatGateway
  ) {}

  @Get()
  @ApiOperation({ summary: 'Get all messages for the authenticated user' })
  @ApiResponse({
    status: 200,
    description: 'Returns all messages for the user',
    type: [MessageDto],
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async getAllMessages(@Request() req: any): Promise<MessageDto[]> {
    const username = req.user.username;

    // Get all messages for the user
    const messages = await this.messageService.getAllMessagesForUsername(username);

    // Process pending messages that this user is receiving
    const pendingMessagesForUser = messages.filter(
      msg => msg.receiver_username === username && msg.status === 'pending'
    );

    // Mark pending messages as delivered and notify senders
    for (const message of pendingMessagesForUser) {
      try {
        // Mark as delivered in database
        await this.messageService.markAsDelivered(message.id);

        // Notify sender about delivery via socket (if sender is online)
        const deliveryNotified = this.chatGateway.notifyMessageDelivered(
          message.sender_username,
          message.id,
          message.receiver_username,
          message.client_message_id
        );

        // Update the message status in the response
        message.status = 'delivered';

        console.log(`Message ${message.id} marked as delivered for ${username}. Sender notified: ${deliveryNotified}`);
      } catch (error) {
        console.error(`Failed to mark message ${message.id} as delivered:`, error);
      }
    }

    // Process delivered messages that this user sent (for delivery confirmations and cleanup)
    const deliveredMessagesSentByUser = messages.filter(
      msg => msg.sender_username === username && msg.status === 'delivered'
    );

    // Delete delivered messages sent by this user immediately since they're actively fetching them
    // This prevents the loophole where messages remain in the database after being delivered
    if (deliveredMessagesSentByUser.length > 0) {
      console.log(`Found ${deliveredMessagesSentByUser.length} delivered messages sent by ${username}`);

      try {
        const messageIds = deliveredMessagesSentByUser.map(msg => msg.id);
        console.log(`Deleting ${messageIds.length} delivered messages for sender ${username}: ${messageIds.join(', ')}`);

        for (const messageId of messageIds) {
          await this.messageService.delete(messageId);
        }

        console.log(`Successfully deleted ${messageIds.length} delivered messages for sender ${username}`);

        // Remove deleted messages from the response
        const deletedMessageIds = new Set(messageIds);
        const filteredMessages = messages.filter(msg => !deletedMessageIds.has(msg.id));
        return filteredMessages;
      } catch (error) {
        console.error(`Failed to delete delivered messages for sender ${username}:`, error);
      }
    }

    return messages;
  }

  @Get('conversation/:otherUsername')
  @ApiOperation({ summary: 'Get messages for a specific conversation' })
  @ApiParam({
    name: 'otherUsername',
    description: 'Username of the other participant in the conversation',
    example: 'johndoe'
  })
  @ApiResponse({
    status: 200,
    description: 'Returns messages for the conversation',
    type: [MessageDto],
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async getConversationMessages(
    @Request() req: any,
    @Param('otherUsername') otherUsername: string
  ): Promise<MessageDto[]> {
    const username = req.user.username;
    return this.messageService.getMessagesForConversation(username, otherUsername);
  }

  @Get('pending')
  @ApiOperation({ summary: 'Get pending messages for the authenticated user' })
  @ApiResponse({
    status: 200,
    description: 'Returns pending messages for the user',
    type: [MessageDto],
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async getPendingMessages(@Request() req: any): Promise<MessageDto[]> {
    const username = req.user.username;
    return this.messageService.getPendingMessagesForUsername(username);
  }


}
